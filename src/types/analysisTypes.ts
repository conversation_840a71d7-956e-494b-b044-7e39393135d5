// Defines the structure for individual analyzed reports and overall analysis data

export interface IAnalyzedReport {
  reportId: string; // Directory name of the report, e.g., "12345"
  issueId?: number;
  title?: string;
  reporter?: string; // email
  disclosedTime?: string; // ISO string format for consistent date handling
  rewardAmount?: number; // USD
  componentPath?: string; // e.g., "Blink>Accessibility"
  componentTags?: string[];
  isClusterfuzz?: boolean;
  isV8?: boolean;
  isWebGPU?: boolean;
}

export interface IReporterStats {
  reporter: string;
  totalReports: number;
  totalReward: number;
  maxSingleReward: number;
  reportIds: string[]; // List of report IDs contributed by this reporter
}

export interface IOverallAnalysis {
  reports: IAnalyzedReport[];
  reporterStats: IReporterStats[];
  lastRefreshed: string; // ISO string, e.g., new Date().toISOString()
  error?: string; // Optional error message if analysis fails
}
