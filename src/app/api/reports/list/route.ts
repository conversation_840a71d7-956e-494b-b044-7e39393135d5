// src/app/api/reports/list/route.ts
import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const REPORTS_BASE_PATH = process.env.REPORTS_BASE_PATH;

if (!REPORTS_BASE_PATH) {
  throw new Error('REPORTS_BASE_PATH environment variable is not set.');
}

export async function GET() {
  try {
    // Check if the base path exists and is a directory
    try {
      const stats = await fs.stat(REPORTS_BASE_PATH!);
      if (!stats.isDirectory()) {
        console.error(`Reports path is not a directory: ${REPORTS_BASE_PATH}`);
        return NextResponse.json(
          { message: 'Server configuration error: Reports path is not a directory.' },
          { status: 500 }
        );
      }
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        console.error(`Reports directory not found: ${REPORTS_BASE_PATH}`);
        return NextResponse.json(
          { message: 'Server configuration error: Reports directory not found.' },
          { status: 500 }
        );
      } 
      // Other fs.stat errors
      console.error(`Error accessing reports path stats: ${REPORTS_BASE_PATH}`, error);
      return NextResponse.json(
        { message: 'Server configuration error: Could not access reports directory.' },
        { status: 500 }
      );
    }

    const entries = await fs.readdir(REPORTS_BASE_PATH!, { withFileTypes: true });
    const reportDirectories = entries
      .filter(entry => entry.isDirectory())
      .map(entry => entry.name);

    return NextResponse.json(reportDirectories, { status: 200 });

  } catch (error) {
    console.error('Failed to list report directories:', error);
    return NextResponse.json(
      { message: 'Internal Server Error: Could not retrieve report list.' },
      { status: 500 }
    );
  }
}
