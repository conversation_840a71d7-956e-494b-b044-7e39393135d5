import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { isValidToken } from '@/lib/jwt';

const COOKIE_NAME = 'auth_token';

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIE_NAME)?.value;

    if (token && await isValidToken(token)) {
      return NextResponse.json({ authenticated: true }, { status: 200 });
    } else {
      return NextResponse.json({ authenticated: false }, { status: 401 });
    }
  } catch (error) {
    console.error('Auth check API error:', error);
    return NextResponse.json({ message: 'An unexpected error occurred during authentication check.' }, { status: 500 });
  }
} 