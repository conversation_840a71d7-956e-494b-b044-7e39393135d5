import { useState, useEffect, useMemo, useTransition } from 'react';
import { IAnalyzedReport } from '../../../types/analysisTypes';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import React from 'react';

export interface FilterTag {
  id: string;
  name: string;
  filters: FilterOptions;
}

export interface FilterOptions {
  titleKeyword: string;
  reporter: string;
  minReward: number | null;
  maxReward: number | null;
  componentPath: string;
  componentTags: string[];
  isClusterfuzz?: boolean;
  isV8?: boolean;
  isWebGPU?: boolean;
}

interface FilterModuleProps {
  reports: IAnalyzedReport[];
  onFilterChange: (filteredReports: IAnalyzedReport[]) => void;
}

// Default tags for common filters
const DEFAULT_TAGS: FilterTag[] = [
  {
    id: 'default-clusterfuzz',
    name: 'Clusterfuzz',
    filters: {
      titleKeyword: '',
      reporter: 'gserviceaccount',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isClusterfuzz: true
    }
  },
  {
    id: 'default-v8',
    name: 'V8',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isV8: true
    }
  },
  {
    id: 'default-webgpu',
    name: 'WebGPU',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isWebGPU: true
    }
  },
  {
    id: 'default-rewarded',
    name: 'Rewarded Reports',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: 1, // At least $1 reward
      maxReward: null,
      componentPath: '',
      componentTags: []
    }
  }
];

interface YearMultiSelectPopoverContentProps {
  allYears: number[];
  selectedYears: number[];
  setSelectedYears: React.Dispatch<React.SetStateAction<number[]>>;
}

const YearMultiSelectPopoverContent: React.FC<YearMultiSelectPopoverContentProps> = React.memo(function YearMultiSelectPopoverContent({ allYears, selectedYears, setSelectedYears }) {
  const [isPending, startTransition] = useTransition();
  return (
    <div className="flex flex-col gap-1 max-h-60 overflow-y-auto">
      {allYears.map((y: number) => (
        <Label key={y} className="flex items-center gap-2 px-2 py-1 cursor-pointer">
          <Checkbox
            checked={selectedYears.includes(y)}
            onCheckedChange={v => {
              startTransition(() => {
                if (v) {
                  setSelectedYears((prev: number[]) => [...prev, y]);
                } else {
                  setSelectedYears((prev: number[]) => prev.filter(yy => yy !== y));
                }
              });
            }}
          />
          {y}年
        </Label>
      ))}
      {isPending && <div className="text-xs text-slate-400 mt-2">Updating...</div>}
    </div>
  );
});

export default function FilterModule({ reports, onFilterChange }: FilterModuleProps) {
  const [filters, setFilters] = useState<FilterOptions>({
    titleKeyword: '',
    reporter: '',
    minReward: null,
    maxReward: null,
    componentPath: '',
    componentTags: [],
  });
  
  const [savedTags, setSavedTags] = useState<FilterTag[]>([]);
  const [tagName, setTagName] = useState('');
  const [showSaveTag, setShowSaveTag] = useState(false);
  const [tagSort, setTagSort] = useState<'count' | 'alpha'>('count');
  const [tagSearch, setTagSearch] = useState('');
  const [includeNAReward, setIncludeNAReward] = useState(false);
  const [timeMode, setTimeMode] = useState<'all' | 'year' | 'custom'>('all');
  const [selectedYears, setSelectedYears] = useState<number[]>([]);
  const [customStart, setCustomStart] = useState<string>('');
  const [customEnd, setCustomEnd] = useState<string>('');
  const [yearPopoverOpen, setYearPopoverOpen] = useState(false);

  // Collect all unique component tags from reports (unused but kept for potential future use)
  // const allComponentTags = Array.from(new Set(reports.flatMap(r => r.componentTags || [])));
  // Collect all unique reporters
  const allReporters = useMemo(() => Array.from(new Set(reports.map(r => r.reporter).filter(Boolean) as string[])), [reports]);
  const [reporterInput, setReporterInput] = useState('');
  const [showReporterDropdown, setShowReporterDropdown] = useState(false);
  const reporterMatches = useMemo(() => {
    if (!reporterInput.trim()) return [];
    return allReporters.filter(r => r.toLowerCase().includes(reporterInput.toLowerCase()));
  }, [reporterInput, allReporters]);

  // Load saved tags from API on mount
  useEffect(() => {
    fetch('/api/reports/tags')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data) && data.length > 0) {
          setSavedTags(data);
        } else {
          setSavedTags(DEFAULT_TAGS);
        }
      })
      .catch(e => {
        console.error('Failed to load saved filter tags:', e);
        setSavedTags(DEFAULT_TAGS);
      });
  }, []);

  // Save tags to API whenever they change (only user-created tags)
  useEffect(() => {
    const userTags = savedTags.filter(tag => !tag.id.startsWith('default-'));
    fetch('/api/reports/tags', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userTags),
    }).catch(e => {
      console.error('Failed to save filter tags:', e);
    });
  }, [savedTags]);

  // For year multi-select
  const allYears = useMemo(() => {
    const years = new Set<number>();
    reports.forEach(r => {
      if (r.disclosedTime) {
        const y = new Date(r.disclosedTime).getFullYear();
        years.add(y);
      }
    });
    return Array.from(years).sort((a, b) => b - a);
  }, [reports]);

  // For tag stats, only apply the time filter (not other filters)
  const reportsForTagStats = useMemo(() => {
    return reports.filter(report => {
      if (timeMode === 'year' && selectedYears.length > 0 && report.disclosedTime) {
        const y = new Date(report.disclosedTime).getFullYear();
        if (!selectedYears.includes(y)) return false;
      } else if (timeMode === 'custom' && report.disclosedTime) {
        const start = customStart ? new Date(customStart) : null;
        const end = customEnd ? new Date(customEnd) : null;
        if (end) end.setDate(end.getDate() + 1);
        const disclosed = new Date(report.disclosedTime);
        if (start && disclosed < start) return false;
        if (end && disclosed >= end) return false;
      }
      return true;
    });
  }, [reports, timeMode, selectedYears, customStart, customEnd]);

  // Tag counts and list based on filtered reports
  const tagCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    reportsForTagStats.forEach(r => (r.componentTags || []).forEach(tag => { counts[tag] = (counts[tag] || 0) + 1; }));
    return counts;
  }, [reportsForTagStats]);
  const allComponentTagsMemo = useMemo(() => Object.keys(tagCounts), [tagCounts]);
  const filteredTags = useMemo(() => {
    let tags = allComponentTagsMemo;
    if (tagSearch.trim()) {
      tags = tags.filter(tag => tag.toLowerCase().includes(tagSearch.toLowerCase()));
    }
    if (tagSort === 'count') {
      tags = tags.sort((a, b) => tagCounts[b] - tagCounts[a] || a.localeCompare(b));
    } else {
      tags = tags.sort((a, b) => a.localeCompare(b));
    }
    return tags;
  }, [allComponentTagsMemo, tagCounts, tagSearch, tagSort]);

  // Compute all reward amounts, min, max, and step
  const rewardAmounts = useMemo(() => reports.map(r => r.rewardAmount).filter(v => typeof v === 'number') as number[], [reports]);
  const rewardMin = useMemo(() => rewardAmounts.length ? Math.min(...rewardAmounts) : 0, [rewardAmounts]);
  const rewardMax = useMemo(() => rewardAmounts.length ? Math.max(...rewardAmounts) : 10000, [rewardAmounts]);
  const rewardStep = useMemo(() => {
    if (rewardAmounts.length < 2) return 1;
    const sorted = [...new Set(rewardAmounts)].sort((a, b) => a - b);
    let minStep = sorted[1] - sorted[0];
    for (let i = 1; i < sorted.length; ++i) {
      const diff = sorted[i] - sorted[i - 1];
      if (diff > 0 && diff < minStep) minStep = diff;
    }
    return minStep;
  }, [rewardAmounts]);
  const [rewardRange, setRewardRange] = useState<[number, number]>([rewardMin, rewardMax]);

  // Sync with filters
  useEffect(() => {
    setRewardRange([
      filters.minReward !== null ? filters.minReward : rewardMin,
      filters.maxReward !== null ? filters.maxReward : rewardMax,
    ]);
  }, [filters.minReward, filters.maxReward, rewardMin, rewardMax]);

  // Memoize filtered reports to avoid unnecessary recalculations
  const filteredReports = useMemo(() => {
    return reports.filter(report => {
      // Time filter
      if (timeMode === 'year' && selectedYears.length > 0 && report.disclosedTime) {
        const y = new Date(report.disclosedTime).getFullYear();
        if (!selectedYears.includes(y)) return false;
      } else if (timeMode === 'custom' && report.disclosedTime) {
        const start = customStart ? new Date(customStart) : null;
        const end = customEnd ? new Date(customEnd) : null;
        if (end) end.setDate(end.getDate() + 1);
        const disclosed = new Date(report.disclosedTime);
        if (start && disclosed < start) return false;
        if (end && disclosed >= end) return false;
      }
      // Title filter
      if (filters.titleKeyword && report.title) {
        if (!report.title.toLowerCase().includes(filters.titleKeyword.toLowerCase())) {
          return false;
        }
      }
      // Reporter filter
      if (filters.reporter && report.reporter) {
        if (!report.reporter.toLowerCase().includes(filters.reporter.toLowerCase())) {
          return false;
        }
      }
      // Reward N/A filter
      if (!includeNAReward && (report.rewardAmount === null || report.rewardAmount === undefined)) {
        return false;
      }
      // Reward range filter
      const minReward = filters.minReward ?? rewardMin;
      const maxReward = filters.maxReward ?? rewardMax;
      if (typeof report.rewardAmount === 'number') {
        if (report.rewardAmount < minReward || report.rewardAmount > maxReward) {
          return false;
        }
      }
      // Component tag filter
      if (filters.componentTags && filters.componentTags.length > 0) {
        if (!report.componentTags || !report.componentTags.includes(filters.componentTags[0])) {
          return false;
        }
      }
      return true;
    });
  }, [reports, filters, includeNAReward, timeMode, selectedYears, customStart, customEnd, rewardMin, rewardMax]);

  // Call the callback when filtered reports change
  useEffect(() => {
    onFilterChange(filteredReports);
  }, [filteredReports, onFilterChange]);

  // Handler for saving current filters as a tag
  const saveAsTag = () => {
    if (!tagName.trim()) return;
    
    const newTag: FilterTag = {
      id: Date.now().toString(),
      name: tagName.trim(),
      filters: { ...filters }
    };
    
    setSavedTags([...savedTags, newTag]);
    setTagName('');
    setShowSaveTag(false);
  };

  // Handler for applying a saved tag
  const applyTag = (tag: FilterTag) => {
    setFilters(tag.filters);
  };

  // Handler for deleting a saved tag
  const deleteTag = (tagId: string) => {
    // Don't allow deletion of default tags
    if (tagId.startsWith('default-')) return;
    
    setSavedTags(savedTags.filter(tag => tag.id !== tagId));
  };

  // Handler for clearing all filters
  const clearFilters = () => {
    setFilters({
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
    });
  };

  // Add a ref to track dragging (for potential future use)
  // const dragging = useRef(false);

  // Handler to update filter only on drag end or blur (for potential future use)
  // const commitRewardRange = () => {
  //   setFilters(f => ({ ...f, minReward: rewardRange[0], maxReward: rewardRange[1] }));
  // };

  // When timeMode changes, open/close year popover accordingly
  useEffect(() => {
    if (timeMode === 'year') {
      setYearPopoverOpen(true);
    } else {
      setYearPopoverOpen(false);
    }
  }, [timeMode]);

  return (
    <div className="bg-slate-800 rounded-lg p-4 mb-6">
      <h2 className="text-lg font-semibold mb-4 text-white">Filter Reports</h2>
      
      {/* Filter inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <Label className="mb-1">Title Contains</Label>
          <Input 
            type="text" 
            value={filters.titleKeyword}
            onChange={(e) => setFilters({...filters, titleKeyword: e.target.value})}
            placeholder="Enter keywords..."
          />
        </div>
        <div>
          <Label className="mb-1">Reporter</Label>
          <div className="relative">
            <Input 
              type="text" 
              value={reporterInput}
              onChange={e => {
                setReporterInput(e.target.value);
                setFilters({...filters, reporter: e.target.value});
                setShowReporterDropdown(true);
              }}
              onFocus={() => { if (reporterMatches.length > 0) setShowReporterDropdown(true); }}
              onBlur={() => setTimeout(() => setShowReporterDropdown(false), 100)}
              placeholder="Reporter name or email..."
              autoComplete="off"
            />
            {showReporterDropdown && reporterMatches.length > 0 && (
              <div className="absolute z-10 left-0 right-0 bg-slate-800 border border-slate-600 rounded-md mt-1 max-h-40 overflow-y-auto shadow-lg">
                {reporterMatches.map(r => (
                  <div
                    key={r}
                    className="px-3 py-2 text-sm text-slate-200 hover:bg-sky-700/30 cursor-pointer"
                    onMouseDown={() => {
                      setFilters({...filters, reporter: r});
                      setReporterInput(r);
                      setShowReporterDropdown(false);
                    }}
                  >
                    {r}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <div>
          <Label className="mb-1">Reward Range ($)</Label>
          <div className="flex gap-2 w-full items-center" style={{ maxWidth: 520 }}>
            <Input
              type="number"
              min={rewardMin}
              max={rewardRange[1]}
              step={rewardStep}
              value={rewardRange[0]}
              onChange={e => {
                const val = Math.max(rewardMin, Math.min(Number(e.target.value), rewardRange[1]));
                setRewardRange([val, rewardRange[1]]);
              }}
              onBlur={() => setFilters(f => ({ ...f, minReward: rewardRange[0] }))}
              onKeyDown={e => { if (e.key === 'Enter') setFilters(f => ({ ...f, minReward: rewardRange[0] })); }}
              className="flex-1"
            />
            <span className="text-slate-400">-</span>
            <Input
              type="number"
              min={rewardRange[0]}
              max={rewardMax}
              step={rewardStep}
              value={rewardRange[1]}
              onChange={e => {
                const val = Math.max(rewardRange[0], Math.min(Number(e.target.value), rewardMax));
                setRewardRange([rewardRange[0], val]);
              }}
              onBlur={() => setFilters(f => ({ ...f, maxReward: rewardRange[1] }))}
              onKeyDown={e => { if (e.key === 'Enter') setFilters(f => ({ ...f, maxReward: rewardRange[1] })); }}
              className="flex-1"
            />
            <Label className="flex items-center gap-2 text-xs select-none mb-0">
              <Checkbox
                checked={includeNAReward}
                onCheckedChange={v => setIncludeNAReward(!!v)}
                className="accent-sky-500"
              />
              Include reports with N/A reward
            </Label>
          </div>
        </div>
        <div>
          <Label className="mb-1">Report Time</Label>
          <div className="flex flex-col gap-2">
            <RadioGroup
              value={timeMode}
              onValueChange={v => setTimeMode(v as typeof timeMode)}
              className="flex gap-4 items-center flex-wrap"
              orientation="horizontal"
            >
              <div className="flex items-center gap-2">
                <RadioGroupItem value="all" id="timeMode-all" />
                <Label htmlFor="timeMode-all">All Time</Label>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="year" id="timeMode-year" />
                <Label htmlFor="timeMode-year">By Year</Label>
                <Popover open={yearPopoverOpen} onOpenChange={setYearPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="h-8 px-3 py-1 text-xs"
                      disabled={timeMode !== 'year'}
                    >
                      {selectedYears.length === 0 ? '选择年份' : selectedYears.sort((a, b) => b - a).map(y => `${y}年`).join(', ')}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-2">
                    <YearMultiSelectPopoverContent
                      allYears={allYears}
                      selectedYears={selectedYears}
                      setSelectedYears={setSelectedYears}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="flex items-center gap-2">
                <RadioGroupItem value="custom" id="timeMode-custom" />
                <Label htmlFor="timeMode-custom">Custom Range</Label>
              </div>
            </RadioGroup>
            {timeMode === 'custom' && (
              <div className="flex gap-2 items-center">
                <Input
                  type="date"
                  value={customStart}
                  onChange={e => setCustomStart(e.target.value)}
                  placeholder="Start date"
                  disabled={timeMode !== 'custom'}
                  className="w-32"
                />
                <span className="text-slate-400">-</span>
                <Input
                  type="date"
                  value={customEnd}
                  onChange={e => setCustomEnd(e.target.value)}
                  placeholder="End date"
                  disabled={timeMode !== 'custom'}
                  className="w-32"
                />
              </div>
            )}
          </div>
        </div>
        <div>
          <Label className="mb-1">Component Tag</Label>
          <div className="flex gap-2 mb-2">
            <Input
              type="text"
              value={tagSearch}
              onChange={e => setTagSearch(e.target.value)}
              placeholder="Search tags..."
              className="flex-1"
            />
            <Select
              value={tagSort}
              onValueChange={v => setTagSort(v as 'count' | 'alpha')}
            >
              <SelectTrigger className="w-28">
                <SelectValue>{tagSort === 'count' ? 'By Count' : 'A-Z'}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="count">By Count</SelectItem>
                <SelectItem value="alpha">A-Z</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="max-h-48 overflow-y-auto rounded-md border border-slate-700 bg-slate-800 shadow-inner">
            <Button
              variant={filters.componentTags.length === 0 ? 'secondary' : 'ghost'}
              className={`w-full text-left px-3 py-2 text-sm ${filters.componentTags.length === 0 ? 'font-semibold' : ''}`}
              onClick={() => setFilters({ ...filters, componentTags: [] })}
            >
              All Tags
            </Button>
            {filteredTags.length === 0 && (
              <div className="px-3 py-2 text-xs text-slate-400">No tags found</div>
            )}
            {filteredTags.map(tag => (
              <Button
                key={tag}
                variant={filters.componentTags[0] === tag ? 'secondary' : 'ghost'}
                className={`w-full text-left px-3 py-2 text-sm flex justify-between items-center ${filters.componentTags[0] === tag ? 'font-semibold' : ''}`}
                onClick={() => setFilters({ ...filters, componentTags: [tag] })}
              >
                <span className="truncate">{tag}</span>
                <span className="ml-2 text-xs text-slate-400">{tagCounts[tag]}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button 
          onClick={clearFilters}
          variant="secondary"
        >
          Clear Filters
        </Button>
        <Button 
          onClick={() => setShowSaveTag(true)}
          variant="default"
        >
          Save as Tag
        </Button>
      </div>
      
      {/* Save tag form */}
      {showSaveTag && (
        <div className="mb-4 p-3 border border-slate-600 rounded-md bg-slate-700">
          <div className="flex gap-2 mb-2">
            <input 
              type="text" 
              value={tagName}
              onChange={(e) => setTagName(e.target.value)}
              className="flex-grow bg-slate-600 border border-slate-500 rounded-md px-3 py-1 text-sm"
              placeholder="Enter tag name..."
            />
            <button 
              onClick={saveAsTag}
              className="bg-green-600 hover:bg-green-500 text-white py-1 px-3 rounded-md text-sm"
              disabled={!tagName.trim()}
            >
              Save
            </button>
            <button 
              onClick={() => setShowSaveTag(false)}
              className="bg-slate-500 hover:bg-slate-400 text-white py-1 px-3 rounded-md text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
      
      {/* Saved tags */}
      {savedTags.length > 0 && (
        <div>
          <h3 className="text-sm font-semibold text-slate-300 mb-2">Filter Tags</h3>
          <div className="flex flex-wrap gap-2">
            {savedTags.map(tag => (
              <div 
                key={tag.id} 
                className={`flex items-center rounded-full px-3 py-1 ${
                  tag.id.startsWith('default-') 
                    ? 'bg-sky-800/50 border border-sky-700' 
                    : 'bg-slate-700'
                }`}
              >
                <button 
                  onClick={() => applyTag(tag)}
                  className="text-sm text-sky-400 hover:text-sky-300 mr-2"
                >
                  {tag.name}
                </button>
                {!tag.id.startsWith('default-') && (
                  <button 
                    onClick={() => deleteTag(tag.id)}
                    className="text-slate-400 hover:text-slate-300"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 