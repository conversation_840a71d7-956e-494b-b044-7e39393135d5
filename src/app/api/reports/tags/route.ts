import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const STORAGE_DIR = path.join(process.cwd(), 'storage');
const TAGS_FILE = path.join(STORAGE_DIR, 'filter_tags.json');

async function ensureStorageDir() {
  await fs.mkdir(STORAGE_DIR, { recursive: true });
}

export async function GET() {
  try {
    await ensureStorageDir();
    const data = await fs.readFile(TAGS_FILE, 'utf-8');
    return NextResponse.json(JSON.parse(data));
  } catch (err: unknown) {
    if (err && typeof err === 'object' && 'code' in err && err.code === 'ENOENT') {
      return NextResponse.json([]); // No tags yet
    }
    const message = err instanceof Error ? err.message : 'Unknown error';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    await ensureStorageDir();
    const tags = await req.json();
    await fs.writeFile(TAGS_FILE, JSON.stringify(tags, null, 2));
    return NextResponse.json({ success: true });
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : 'Unknown error';
    return NextResponse.json({ error: message }, { status: 500 });
  }
} 