import { NextRequest, NextResponse } from 'next/server';
import { getAnalysisResults } from '@/lib/analysisService'; // Using @ alias for cleaner imports

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const forceRefreshParam = searchParams.get('forceRefresh');
  const forceRefresh = forceRefreshParam === 'true';

  try {
    const analysisData = await getAnalysisResults(forceRefresh);
    if (analysisData && analysisData.error) {
      // If the service itself caught an error and returned it in the payload
      return NextResponse.json({ message: 'Analysis failed', error: analysisData.error, details: analysisData }, { status: 500 });
    }
    if (!analysisData) {
      // Should not happen if service handles errors, but as a fallback
      return NextResponse.json({ message: 'Failed to retrieve analysis data', error: 'Unknown error' }, { status: 500 });
    }
    return NextResponse.json(analysisData);
  } catch (error: unknown) {
    console.error('[API /api/analysis] Error:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: 'An unexpected error occurred on the server.', error: message }, { status: 500 });
  }
}
