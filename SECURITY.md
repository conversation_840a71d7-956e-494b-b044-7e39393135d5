# Security Implementation

## JWT Authentication System

This application now uses a secure JWT (JSON Web Token) based authentication system instead of the previous insecure cookie-based approach.

### Security Improvements

1. **Server-side Token Generation**: JWT tokens are generated server-side with cryptographic signatures
2. **Token Verification**: All authentication checks verify the JWT signature and expiration
3. **HttpOnly Cookies**: Tokens are stored in httpOnly cookies to prevent XSS attacks
4. **Secure Configuration**: Proper cookie settings (secure, sameSite, etc.)

### Environment Variables

**Required Environment Variables:**

- `PIN_CODE`: Your secure PIN for authentication
- `JWT_SECRET`: A strong, random secret key for JWT signing (minimum 32 characters recommended)
- `JWT_EXPIRES_IN`: Token expiration time (default: 24h)

**Generating a Secure JWT Secret:**

```bash
# Generate a secure random secret
openssl rand -base64 32
```

### Security Best Practices

1. **JWT Secret**: 
   - Use a strong, random secret key
   - Never commit the secret to version control
   - Rotate the secret periodically

2. **PIN Code**:
   - Use a strong, unique PIN
   - Consider using a longer passphrase instead of a simple PIN

3. **HTTPS**:
   - Always use HTTPS in production
   - The `secure` cookie flag is automatically set in production

4. **Token Expiration**:
   - Tokens expire automatically (default: 24 hours)
   - Users must re-authenticate after expiration

### How It Works

1. **Login**: User enters PIN → Server verifies PIN → Server generates signed JWT → JWT stored in httpOnly cookie
2. **Authentication**: Each request → Middleware extracts JWT from cookie → Verifies signature and expiration → Allows/denies access
3. **Logout**: Server clears the JWT cookie

### Migration from Old System

The old system used a simple `app_pin_verified=true` cookie which could be easily forged. The new system:

- Uses cryptographically signed tokens that cannot be forged without the secret key
- Includes expiration times to limit token lifetime
- Provides proper server-side validation

### Token Structure

JWT tokens contain:
- `authenticated: true` - Authentication status
- `iat` - Issued at timestamp
- `exp` - Expiration timestamp
- Cryptographic signature using the JWT_SECRET

### Troubleshooting

If you encounter authentication issues:

1. Check that `JWT_SECRET` is set in your environment
2. Verify the token hasn't expired
3. Ensure cookies are being sent with requests
4. Check browser developer tools for cookie settings
