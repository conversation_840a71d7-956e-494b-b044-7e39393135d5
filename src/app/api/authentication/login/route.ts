// src/app/api/authentication/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { generateToken } from '@/lib/jwt';

const PIN_CODE = process.env.PIN_CODE;
const COOKIE_NAME = 'auth_token';

export async function POST(request: NextRequest) {
  if (!PIN_CODE) {
    console.error('PIN_CODE is not set in environment variables.');
    return NextResponse.json({ message: 'Server configuration error. Contact administrator.' }, { status: 500 });
  }

  try {
    const { pin } = await request.json();

    if (pin === PIN_CODE) {
      // Generate JWT token
      const token = await generateToken();

      const oneDay = 24 * 60 * 60; // 24 hours in seconds
      const cookieStore = await cookies();
      cookieStore.set(COOKIE_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: oneDay,
        path: '/',
      });
      return NextResponse.json({ message: 'Login successful' }, { status: 200 });
    } else {
      return NextResponse.json({ message: 'Invalid PIN' }, { status: 401 });
    }
  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json({ message: 'An unexpected error occurred.' }, { status: 500 });
  }
}
