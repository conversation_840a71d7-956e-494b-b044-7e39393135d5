// src/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { isValidToken } from '@/lib/jwt';

const COOKIE_NAME = 'auth_token';
const PIN_CODE = process.env.PIN_CODE;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow requests to static files, API routes (especially auth), and the login page itself
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') || // if you have a /public/static folder
    pathname.startsWith('/api/authentication/') ||
    pathname === '/login' ||
    pathname === '/favicon.ico' 
  ) {
    return NextResponse.next();
  }

  if (!PIN_CODE) {
    console.error("CRITICAL: PIN_CODE is not set. Authentication cannot proceed.");
    if (pathname !== '/login') {
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('error', 'server_config');
        return NextResponse.redirect(loginUrl);
    }
    // If already on login page, or if PIN_CODE is not set and we are on login, let it render to show error from API potentially
    return NextResponse.next(); 
  }

  const token = request.cookies.get(COOKIE_NAME)?.value;
  const isAuthenticated = token ? await isValidToken(token) : false;

  if (!isAuthenticated) {
    const loginUrl = new URL('/login', request.url);
    if (pathname !== '/' && !pathname.startsWith('/api/')) { // Don't set 'next' for API routes or root
        loginUrl.searchParams.set('next', pathname);
    }
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * This simpler matcher covers all paths, and the logic inside the middleware
     * explicitly allows certain paths like /_next/*, /api/auth/*, /login.
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
