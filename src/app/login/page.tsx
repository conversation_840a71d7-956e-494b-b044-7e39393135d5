// src/app/login/page.tsx
'use client';

import { useState, FormEvent, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function LoginPage() {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check for server_config error from middleware
    if (searchParams.get('error') === 'server_config') {
      setError('Server configuration error. PIN_CODE might be missing.');
    }
  }, [searchParams]);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/authentication/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pin }),
      });

      if (response.ok) {
        const nextPath = searchParams.get('next');
        router.push(nextPath || '/'); 
      } else {
        const data = await response.json();
        setError(data.message || 'Invalid PIN');
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-slate-900 to-slate-800 p-4">
      <div className="w-full max-w-md rounded-xl bg-slate-800/50 p-8 shadow-2xl backdrop-blur-lg">
        <h1 className="mb-2 text-center text-3xl font-bold text-sky-400">
          {process.env.NEXT_PUBLIC_APP_NAME || 'Dashboard'}
        </h1>
        <p className="mb-8 text-center text-sm text-slate-400">Enter PIN to access</p>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="pin" className="sr-only">
              PIN Code
            </label>
            <input
              id="pin"
              name="pin"
              type="password"
              autoComplete="current-password"
              required
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              placeholder="PIN Code"
              className="w-full rounded-lg border border-slate-700 bg-slate-900/80 px-4 py-3 text-slate-200 placeholder-slate-500 shadow-sm focus:border-sky-500 focus:outline-none focus:ring-2 focus:ring-sky-500/50"
            />
          </div>

          {error && (
            <p className="text-center text-sm text-red-400" role="alert">
              {error}
            </p>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full rounded-lg bg-sky-600 px-4 py-3 font-semibold text-white shadow-md transition-colors duration-150 hover:bg-sky-500 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-slate-900 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isLoading ? (
                <svg className="mx-auto h-5 w-5 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                'Unlock'
              )}
            </button>
          </div>
        </form>
      </div>
      <footer className="mt-12 text-center text-xs text-slate-500">
        &copy; {new Date().getFullYear()} Access Restricted.
      </footer>
    </div>
  );
}
