// src/app/api/reports/[reportId]/core/route.ts
import { NextRequest, NextResponse } from 'next/server';
import fsPromises from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { ParsedRawIssue, RawJsonData } from '../../../../../lib/parser/core_parser'; // Updated import

const REPORTS_BASE_PATH = process.env.REPORTS_BASE_PATH;

if (!REPORTS_BASE_PATH || !existsSync(REPORTS_BASE_PATH)) {
  throw new Error('REPORTS_BASE_PATH environment variable is not set.');
}

interface RouteParams {
  params: Promise<{
    reportId: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  const { reportId } = await params;

  if (!reportId) {
    return NextResponse.json({ message: 'Report ID is required.' }, { status: 400 });
  }

  // Attempt to convert reportId to a number for the parser
  const issueNumber = parseInt(reportId, 10);
  if (isNaN(issueNumber)) {
    console.error(`Invalid reportId format: ${reportId}. Must be a number.`);
    return NextResponse.json({ message: 'Invalid Report ID format. Must be a number.' }, { status: 400 });
  }

  try {
    const reportPath = path.join(REPORTS_BASE_PATH!, reportId);
    // Validate reportId to prevent directory traversal issues.
    // path.basename will extract the last portion, which should be the reportId itself.
    // This ensures we are not constructing a path like /Users/<USER>/source/CrbugSnapshot/snapshot/../../../etc
    if (path.basename(reportPath) !== reportId) {
        console.error(`Potentially unsafe reportId: ${reportId}`);
        return NextResponse.json({ message: 'Invalid Report ID.' }, { status: 400 });
    }

    const coreJsonPath = path.join(reportPath!, 'core.json');

    try {
      await fsPromises.access(coreJsonPath, fsPromises.constants.F_OK);
    } catch (error) {
      console.error(`core.json not found for report ${reportId} at ${coreJsonPath}`);
      return NextResponse.json(
        { message: `Core.json not found for report ${reportId}.` },
        { status: 404 }
      );
    }

    const fileContents = await fsPromises.readFile(coreJsonPath, 'utf8');
    const rawJsonData: RawJsonData = JSON.parse(fileContents);

    // Use the parser
    const parsedIssue = new ParsedRawIssue(issueNumber, rawJsonData);
    
    // The ParsedRawIssue class might have methods that aren't plain data (like getSanitizedName on Attachment).
    // NextResponse.json will serialize the public properties of the object.
    // If specific serialization is needed (e.g., to ensure methods are not lost or to transform data),
    // a toJSON() method could be added to ParsedRawIssue or its members, or manual construction of the response object here.
    return NextResponse.json(parsedIssue, { status: 200 });

  } catch (error: any) {
    if (error instanceof SyntaxError) {
      console.error(`Failed to parse core.json for report ${reportId}:`, error);
      return NextResponse.json(
        { message: `Error parsing core.json for report ${reportId}. File might be corrupted.` },
        { status: 500 }
      );
    }
    console.error(`Failed to retrieve or parse core.json for report ${reportId}:`, error);
    return NextResponse.json(
      { message: `Internal Server Error: Could not retrieve or parse core.json for report ${reportId}.` },
      { status: 500 }
    );
  }
}
