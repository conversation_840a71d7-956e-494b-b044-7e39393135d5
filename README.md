# Crbug Snapshot Dashboard

A dashboard for viewing and filtering Chrome bug reports.

## Features

- View Chrome bug reports with detailed information
- Filter reports using multiple criteria:
  - Title keyword search
  - Reporter filtering
  - Reward amount filtering (minimum/maximum)
  - Component path filtering
- Save filter combinations as custom tags for quick access
- Default tags for common filters (Clusterfuzz, V8, WebGPU, Rewarded Reports)

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Usage

### Filtering Reports

1. Navigate to the Reports page
2. Use the filter inputs to narrow down reports:
   - Enter keywords to search in report titles
   - Filter by reporter name or email
   - Set minimum/maximum reward amounts
   - Filter by component path
3. Clear filters using the "Clear Filters" button

### Saving and Using Filter Tags

1. Set up your desired filter combination
2. Click "Save as Tag"
3. Enter a name for your filter tag
4. Click "Save"
5. Use saved tags by clicking on them in the "Filter Tags" section
6. User-created tags can be deleted by clicking the "x" button

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
