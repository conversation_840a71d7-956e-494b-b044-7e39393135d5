'use client';

import { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { IAnalyzedReport } from '../../types/analysisTypes';
import FilterModule from './components/FilterModule';

interface IOverallAnalysis {
  reports: IAnalyzedReport[];
  lastRefreshed?: string;
  error?: string;
  reporterStats?: any;
}

interface ReportListPageProps {
  title: string;
}

/** Helper to get the longest string from an array */
function getLongestString(arr: string[] | undefined | null): string | null {
  if (!arr || arr.length === 0) return null;
  return arr.reduce((a, b) => (a.length >= b.length ? a : b));
}

export default function ReportListPage({ title }: ReportListPageProps) {
  const [analysisResult, setAnalysisResult] = useState<IOverallAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filteredReports, setFilteredReports] = useState<IAnalyzedReport[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchData = async (forceRefresh = false) => {
    setIsLoading(true);
    setError(null);
    try {
      const url = forceRefresh ? '/api/analysis?refresh=1' : '/api/analysis';
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch analysis data');
      }
      const data: IOverallAnalysis = await response.json();
      setAnalysisResult(data);
      setFilteredReports(data.reports || []);
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleFilterChange = useCallback((filteredReports: IAnalyzedReport[]) => {
    setFilteredReports(filteredReports);
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchData(true);
  };

  if (isLoading) return (
    <div className="flex min-h-screen items-center justify-center bg-slate-900">
      <span className="inline-block w-12 h-12 border-4 border-sky-400 border-t-transparent rounded-full animate-spin" />
    </div>
  );
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;
  if (!analysisResult) return <div className="p-4">No data available</div>;

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="container mx-auto p-4">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-sky-400">{title}</h1>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2 bg-sky-700 hover:bg-sky-600 text-white px-4 py-2 rounded-md shadow disabled:opacity-60 disabled:cursor-not-allowed"
          >
            {isRefreshing && (
              <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            )}
            Refresh Analysis
          </button>
        </div>

        {/* Include the FilterModule component */}
        {analysisResult.reports && (
          <FilterModule 
            reports={analysisResult.reports} 
            onFilterChange={handleFilterChange} 
          />
        )}

        {filteredReports.length === 0 ? (
          <div className="p-4 bg-slate-800/50 rounded-lg">
            <p className="text-slate-400">No reports found matching the current filters.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredReports.map((report) => {
              const longestTag = Array.isArray(report.componentTags) && report.componentTags.length > 0
                ? report.componentTags.reduce((a, b) => (a.length >= b.length ? a : b))
                : null;
              const rewardIsNumber = typeof report.rewardAmount === 'number';
              const rewardIsZeroOrNA = !rewardIsNumber || report.rewardAmount === 0;
              return (
                <Link 
                  key={report.reportId} 
                  href={`/reports/${report.reportId}`}
                  className="block bg-slate-700/50 rounded-md shadow p-4 hover:bg-slate-600/50 transition-colors duration-150"
                >
                  <h2 className="text-md font-semibold text-sky-400 truncate" title={report.title || 'N/A'}>
                    {report.title || `Report ${report.reportId}`}
                  </h2>
                  {/* ID and Reporter on the same line */}
                  <div className="flex items-center text-xs mt-1 gap-4">
                    <span className="text-slate-300 font-mono" style={{ whiteSpace: 'pre' }}>ID: {(report.issueId || report.reportId).toString().padEnd(10, ' ')}</span>
                    <span className="text-slate-400">Reporter: <span className="font-medium text-slate-300" title={report.reporter || 'N/A'}>{report.reporter || 'N/A'}</span></span>
                  </div>
                  {/* Reward and Component Tag on the same line */}
                  <div className="flex items-center text-xs mt-1 gap-4">
                    <span className={rewardIsZeroOrNA ? 'text-slate-400 font-normal' : 'text-green-400 font-semibold'} style={{ whiteSpace: 'pre' }}>
                      Reward: {rewardIsNumber ? `$${report.rewardAmount?.toLocaleString()}`.padEnd(8, ' ') : 'N/A'.padEnd(8, ' ')}
                    </span>
                    {longestTag && (
                      <span className="text-slate-400 truncate max-w-[60%]" title={longestTag}>
                        Component Tag: <span className="font-medium text-slate-300">{longestTag}</span>
                      </span>
                    )}
                  </div>
                </Link>
              );
            })}
          </div>
        )}

        <div className="mt-8 text-center">
          <Link 
            href="/" 
            className="inline-flex items-center text-sky-400 hover:text-sky-300 transition-colors"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
