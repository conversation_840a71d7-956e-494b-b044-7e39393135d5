// src/lib/parser/core_parser.ts
import * as path from 'path';

interface IAttachment {
    attachmentId: number;
    mimeType: string;
    size: number | null;
    name: string;
    contentId: string;
    getSanitizedName(): string;
}

export class Attachment implements IAttachment {
    public attachmentId: number;
    public mimeType: string;
    public size: number | null;
    public name: string;
    public contentId: string;

    constructor(attachmentId: number, mimeType: string, size: number | null, name: string, contentId: string) {
        this.attachmentId = attachmentId;
        this.mimeType = mimeType;
        this.size = size;
        this.name = name;
        this.contentId = contentId;
    }

    public getSanitizedName(): string {
        let sanitized = path.basename(this.name || '');
        const invalidChars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*'];
        for (const char of invalidChars) {
            sanitized = sanitized.replace(new RegExp(char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '_');
        }
        if (!sanitized) {
            sanitized = "attachment";
        }
        return `${this.attachmentId}_${sanitized}`;
    }
}

export interface IRawComment {
    user: string;
    timestamp: number;
    content: string;
    attachments: IAttachment[];
    customFields: Record<string, string[]>;
}

export class RawComment implements IRawComment {
    public user: string;
    public timestamp: number;
    public content: string;
    public attachments: IAttachment[];
    public customFields: Record<string, string[]>;

    constructor(user: string, timestamp: number, content: string, attachments: IAttachment[] | null = null, customFields: Record<string, string[]> | null = null) {
        this.user = user;
        this.timestamp = timestamp;
        this.content = content;
        this.attachments = attachments || [];
        this.customFields = customFields || {};
    }
}

export const CustomFieldID: Record<string, string> = {
    COMPONENT_TAGS: "1222907",
    COMPONENT: "1253656",
    CHROMIUM_LABELS: "1223031",
    MERGE: "1223087",
    REWARD: "1223135"
};

export type RawJsonData = unknown;

export interface IParsedRawIssue {
    issueNumber: number;
    rawData: RawJsonData;
    comments: IRawComment[];
    aggregatedAttachments: Record<string, IAttachment>;
    componentTags: string[];
    componentPaths: string[];
    chromiumLabels: string[];
    title: string | null;
    severity: string | number | null;
    status: string | number | null;
    type: string | number | null;
    reporter: string | null;
    reward_amount: number | null;
    hasAttachments(): boolean;
}

export class ParsedRawIssue implements IParsedRawIssue {
    public issueNumber: number;
    public rawData: RawJsonData;
    public comments: IRawComment[];
    public aggregatedAttachments: Record<string, IAttachment>;
    public componentTags: string[];
    public componentPaths: string[];
    public chromiumLabels: string[];
    public title: string | null;
    public severity: string | number | null;
    public status: string | number | null;
    public type: string | number | null;
    public reporter: string | null;
    public reward_amount: number | null;

    constructor(issueNumber: number, rawData: RawJsonData) {
        this.issueNumber = issueNumber;
        this.rawData = rawData;
        this.comments = this._parseRawDataToComments(rawData);
        this.aggregatedAttachments = {};

        this.componentTags = [];
        this.componentPaths = [];
        this.chromiumLabels = [];
        this.reward_amount = null;

        for (const comment of this.comments) {
            const componentTagsFieldKey = CustomFieldID.COMPONENT_TAGS;
            const componentFieldKey = CustomFieldID.COMPONENT;
            const chromiumLabelsFieldKey = CustomFieldID.CHROMIUM_LABELS;
            const rewardFieldKey = CustomFieldID.REWARD;

            if (comment.customFields[componentTagsFieldKey]) {
                this.componentTags = comment.customFields[componentTagsFieldKey];
            }
            if (comment.customFields[componentFieldKey]) {
                this.componentPaths = comment.customFields[componentFieldKey];
            }
            if (comment.customFields[chromiumLabelsFieldKey]) {
                this.chromiumLabels = comment.customFields[chromiumLabelsFieldKey];
            }
            if (comment.customFields[rewardFieldKey]) {
                this.reward_amount = Number(comment.customFields[rewardFieldKey][0]);
            }

            for (const attachment of comment.attachments) {
                if (attachment.size === null || attachment.size === undefined || attachment.size > 5 * 1024 * 1024) {
                    continue;
                }
                this.aggregatedAttachments[attachment.contentId] = attachment;
            }
        }

        this.title = this._extractFieldValue(rawData, "title") as string | null;
        this.severity = this._extractFieldValue(rawData, "severity");
        this.status = this._extractFieldValue(rawData, "status");
        this.type = this._extractFieldValue(rawData, "type");
        this.reporter = this._extractFieldValue(rawData, "reporter") as string | null;
    }

    public hasAttachments(): boolean {
        return Object.keys(this.aggregatedAttachments).length > 0;
    }

    private _parseCustomFieldValue(rawDataArray: unknown): [number, string[]] {
        console.assert(rawDataArray[0] === "custom_fields", "Expected custom_fields marker");
        if (!Array.isArray(rawDataArray) || rawDataArray.length < 3 || !Array.isArray(rawDataArray[2])) {
            return [0, []];
        }

        if (rawDataArray[2].length === 1) {
            const coreValueMeta = rawDataArray[2][0][1];
            const fieldId = parseInt(coreValueMeta[0], 10);
            return [fieldId, []];
        }

        if (rawDataArray[2].length >= 2 && Array.isArray(rawDataArray[2][1])) {
            const coreValueMeta = rawDataArray[2][1][1];
            if (!Array.isArray(coreValueMeta) || coreValueMeta.length < 1) {
                return [0, []];
            }

            const fieldId = parseInt(coreValueMeta[0], 10);
            const valueAsString = coreValueMeta.length > 1 ? coreValueMeta[coreValueMeta.length - 1] : "";
            let valueAsList: string[] = [];

            if (coreValueMeta.length > 3 && coreValueMeta[coreValueMeta.length - 3] !== null && coreValueMeta[coreValueMeta.length - 3] !== undefined) {
                if (Array.isArray(coreValueMeta[coreValueMeta.length - 3]) && coreValueMeta[coreValueMeta.length - 3].length > 0 && Array.isArray(coreValueMeta[coreValueMeta.length - 3][0])) {
                    valueAsList = coreValueMeta[coreValueMeta.length - 3][0];
                }
            } else if (coreValueMeta.length > 2 && coreValueMeta[coreValueMeta.length - 2] !== null && coreValueMeta[coreValueMeta.length - 2] !== undefined) {
                if (Array.isArray(coreValueMeta[coreValueMeta.length - 2]) && coreValueMeta[coreValueMeta.length - 2].length > 0 && Array.isArray(coreValueMeta[coreValueMeta.length - 2][0])) {
                    valueAsList = coreValueMeta[coreValueMeta.length - 2][0];
                }
            } else if (valueAsString && typeof valueAsString === 'string') {
                valueAsList = valueAsString.split(",").map(v => v.trim());
            }
            return [fieldId, valueAsList];
        }
        return [0, []];
    }

    private _parseRawDataToComments(rawJson: RawJsonData): IRawComment[] {
        const comments: IRawComment[] = [];
        console.assert(Array.isArray(rawJson) && rawJson.length >= 1, "Raw JSON root is not an array or is empty");
        if (!Array.isArray(rawJson) || rawJson.length < 1) return comments;

        console.assert(Array.isArray(rawJson[0]) && rawJson[0].length >= 3 && Array.isArray(rawJson[0][2]), "Events structure is invalid");
        if (!Array.isArray(rawJson[0]) || rawJson[0].length < 3 || !Array.isArray(rawJson[0][2])) return comments;
        
        const events = rawJson[0][2];

        for (const event of events) {
            console.assert(Array.isArray(event) && event.length >= 3, "Event structure is invalid");
            if (!Array.isArray(event) || event.length < 3) continue;

            if (Array.isArray(event[0]) && event[0].length >= 2 && Array.isArray(event[1]) && event[1].length >= 1) {
                const customFields: Record<string, string[]> = {};
                if (event.length > 5 && Array.isArray(event[5]) && event[5].length > 0) {
                    const allCustomFields = event[5];
                    for (const singleCustomField of allCustomFields) {
                        if (Array.isArray(singleCustomField) && singleCustomField.length > 0 && singleCustomField[0] === "custom_fields") {
                            const [fieldIdNum, fieldValue] = this._parseCustomFieldValue(singleCustomField);
                            if (fieldIdNum) { // Ensure fieldId is not 0
                                customFields[String(fieldIdNum)] = fieldValue;
                            }
                        }
                    }
                }

                let content = "";
                if (Array.isArray(event[2]) && event[2].length >= 1 && typeof event[2][0] === 'string') {
                    content = event[2][0];
                }

                const attachments: IAttachment[] = [];
                if (Array.isArray(event[2]) && event[2].length >= 2 && Array.isArray(event[2][1])) {
                    for (const attachmentData of event[2][1]) {
                        if (Array.isArray(attachmentData) && attachmentData.length >= 5) {
                            const attachmentId: number = attachmentData[0];
                            const mimeType: string = attachmentData[1] || "application/octet-stream";
                            const size: number | null = attachmentData[2]; 
                            const name: string = attachmentData[3] || `attachment_${attachmentId}`;
                            const contentIdArray: [string] | undefined | null = attachmentData[4];
                            const contentId: string = (Array.isArray(contentIdArray) && contentIdArray.length > 0) ? contentIdArray[0] : "";

                            if (size === null && name === "deleted") {
                                continue;
                            }
                            attachments.push(new Attachment(attachmentId, mimeType, size, name, contentId));
                        }
                    }
                }
                
                const user: string = (Array.isArray(event[0]) && event[0].length >=2 && typeof event[0][1] === 'string') ? event[0][1] : "";
                const timestamp: number = (Array.isArray(event[1]) && event[1].length >=1 && typeof event[1][0] === 'number') ? event[1][0] : 0;

                comments.push(new RawComment(user, timestamp, content, attachments, customFields));
            }
        }
        return comments;
    }

    private _extractFieldValue(rawJson: RawJsonData, fieldName: string): string | number | null {
        console.assert(rawJson && Array.isArray(rawJson) && rawJson.length >= 1, "_extractFieldValue: rawJson is invalid (1)");
        if (!rawJson || !Array.isArray(rawJson) || rawJson.length < 1) return null;
        
        console.assert(rawJson[0] && Array.isArray(rawJson[0]) && rawJson[0].length >=3 && rawJson[0][2] && Array.isArray(rawJson[0][2]) && rawJson[0][2].length >=1 && rawJson[0][2][0] && Array.isArray(rawJson[0][2][0]) && rawJson[0][2][0].length >=6 && rawJson[0][2][0][5] && Array.isArray(rawJson[0][2][0][5]), "_extractFieldValue: rawJson structure is invalid for fastCore (2)");
        if (!rawJson[0] || !Array.isArray(rawJson[0]) || rawJson[0].length <3 || !rawJson[0][2] || !Array.isArray(rawJson[0][2]) || rawJson[0][2].length <1 || !rawJson[0][2][0] || !Array.isArray(rawJson[0][2][0]) || rawJson[0][2][0].length <6 || !rawJson[0][2][0][5] || !Array.isArray(rawJson[0][2][0][5])) return null;

        const fastCore = rawJson[0][2][0][5];

        for (const singleField of fastCore) {
            if (Array.isArray(singleField) && singleField[0] === fieldName) {
                if (["status", "type", "priority", "title", "severity", "component_id"].includes(fieldName)) {
                    try { return singleField[2][1][1][0]; } catch { /* continue */ }
                } else if (fieldName === "reporter") {
                    try { return singleField[2][1][1][1]; } catch { /* continue */ }
                }
            }
        }
        
        let alternativeMetadataList: unknown;
        try {
            alternativeMetadataList = (rawJson as any)[0][2][0][3];
            if (Array.isArray(alternativeMetadataList)) {
                 for (const item of alternativeMetadataList) {
                    if (Array.isArray(item) && item.length >=3 && item[0] === fieldName) {
                        if (fieldName === "title") {
                            if (item.length >= 3 && Array.isArray(item[2]) && item[2].length >= 2 && Array.isArray(item[2][1]) && item[2][1].length >=2 && Array.isArray(item[2][1][1]))
                                return item[2][1][1][0];
                        } else if (["severity", "status", "type"].includes(fieldName)) {
                            if (item.length >= 3 && Array.isArray(item[2]) && item[2].length >= 2 && Array.isArray(item[2][1]) && item[2][1].length >=2 && Array.isArray(item[2][1][1]))
                                return item[2][1][1][0];
                        } else if (fieldName === "reporter") {
                            if (item.length >= 3 && Array.isArray(item[2]) && item[2].length >= 2 && Array.isArray(item[2][1]) && item[2][1].length >=3 && Array.isArray(item[2][1][1]))
                                return item[2][1][1][1]; 
                        }
                    }
                }
            }
        } catch {
            // console.warn(`Error accessing alternative metadata for ${fieldName}`);
        }
        return null;
    }
}
