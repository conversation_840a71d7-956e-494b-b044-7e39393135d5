// src/app/reports/[reportId]/page.tsx
'use client';

import { useParams } from 'next/navigation'; // Removed useRouter as it's not used
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { ParsedRawIssue, RawComment, Attachment } from '../../../lib/parser/core_parser'; // Import parser types
import ReactMarkdown from 'react-markdown'; // Import ReactMarkdown

// Helper function to format timestamp (seconds to readable date)
const formatTimestamp = (timestamp: number): string => {
  if (!timestamp) return 'N/A';
  return new Date(timestamp * 1000).toLocaleString();
};

export default function ReportDetailPage() {
  const params = useParams();
  const reportId = params.reportId as string;

  const [parsedIssue, setParsedIssue] = useState<ParsedRawIssue | null>(null); // Updated state type
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!reportId) {
      setError("Report ID is missing.");
      setIsLoading(false);
      return;
    }

    const fetchParsedIssue = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/reports/${reportId}/core`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to fetch report data: ${response.statusText}`);
        }
        const data: ParsedRawIssue = await response.json();
        setParsedIssue(data);
      } catch (err: any) {
        setError(err.message || 'An unexpected error occurred while fetching report details.');
        console.error(`Fetch parsed issue data error for ${reportId}:`, err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchParsedIssue();
  }, [reportId]);

  // Helper to render a list of strings (tags, labels)
  const renderStringList = (items: string[] | undefined | null, title: string) => {
    if (!items || items.length === 0) return null;
    return (
      <div className="mb-3">
        <h4 className="text-md font-semibold text-slate-400">{title}:</h4>
        <div className="flex flex-wrap gap-2 mt-1">
          {items.map((item, index) => (
            <span key={index} className="px-2 py-1 text-xs bg-sky-700 text-sky-100 rounded-full">
              {item}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="flex min-h-screen flex-col bg-slate-900 text-white">
      <header className="bg-slate-800/50 backdrop-blur-md shadow-lg p-4 sticky top-0 z-50">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold text-sky-400">
            <Link href="/" className="hover:text-sky-300 transition-colors">
              {process.env.NEXT_PUBLIC_APP_NAME || 'Crbug Snapshot Dashboard'}
            </Link>
            {reportId && <span className="text-slate-400"> / Report {reportId}</span>}
          </h1>
        </div>
      </header>
      <main className="flex-grow container mx-auto p-4 md:p-8">
        <div className="mb-6">
          <Link
            href="/"
            className="text-sky-400 hover:text-sky-300 transition-colors inline-flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5 mr-2">
              <path fillRule="evenodd" d="M17 10a.75.75 0 0 1-.75.75H5.612l4.158 3.96a.75.75 0 1 1-1.04 1.08l-5.5-5.25a.75.75 0 0 1 0-1.08l5.5-5.25a.75.75 0 1 1 1.04 1.08L5.612 9.25H16.25A.75.75 0 0 1 17 10Z" clipRule="evenodd" />
            </svg>
            Back to Dashboard
          </Link>
        </div>

        {isLoading && <div className="text-center py-10"><p className="text-slate-400 text-lg">Loading report details...</p></div>}
        {error && <div className="bg-red-900/30 border border-red-700 text-red-300 p-4 rounded-md"><p>Error: {error}</p></div>}
        
        {!isLoading && !error && parsedIssue && (
          <div className="bg-slate-800 shadow-xl rounded-lg p-6 space-y-6">
            <div className="space-y-4">
              <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                <h2 className="text-3xl font-bold text-sky-300 break-all">
                  {parsedIssue.title || `Report ${reportId}`}
                </h2>
                <div className="flex flex-wrap gap-2">
                  {parsedIssue.reward_amount !== null && (
                    <div className="text-green-400 text-sm bg-green-900/30 px-3 py-1 rounded-md">
                      ${parsedIssue.reward_amount.toLocaleString()}
                    </div>
                  )}
                  <div className="text-slate-400 text-sm bg-slate-700/50 px-3 py-1 rounded-md">
                    {parsedIssue.status || 'Status: N/A'}
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <p><strong className="text-slate-400">Issue ID:</strong> {parsedIssue.issueNumber}</p>
                <p><strong className="text-slate-400">Reporter:</strong> {parsedIssue.reporter || 'N/A'}</p>
                <p><strong className="text-slate-400">Severity:</strong> {parsedIssue.severity || 'N/A'}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <p><strong className="text-slate-400">Reporter:</strong> {parsedIssue.reporter || 'N/A'}</p>
              <p><strong className="text-slate-400">Status:</strong> {parsedIssue.status || 'N/A'}</p>
              <p><strong className="text-slate-400">Severity:</strong> {parsedIssue.severity || 'N/A'}</p>
              <p><strong className="text-slate-400">Type:</strong> {parsedIssue.type || 'N/A'}</p>
            </div>

            {renderStringList(parsedIssue.componentTags, 'Component Tags')}
            {renderStringList(parsedIssue.componentPaths, 'Component Paths')}
            {renderStringList(parsedIssue.chromiumLabels, 'Chromium Labels')}
            
            {/* Comments Section */}
            <div>
              <h3 className="text-2xl font-semibold text-sky-300 mt-8 mb-4 border-b border-slate-700 pb-2">Comments ({parsedIssue.comments?.length || 0})</h3>
              {parsedIssue.comments && parsedIssue.comments.length > 0 ? (
                <ul className="space-y-6">
                  {parsedIssue.comments
                    .filter(comment => comment.content || comment.attachments?.length > 0)
                    .map((comment: RawComment, index: number) => (
                    <li key={index} className="p-4 bg-slate-700/80 rounded-lg shadow">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-semibold text-sky-400">{comment.user || 'Anonymous'}</span>
                        <span className="text-xs text-slate-400">{formatTimestamp(comment.timestamp)}</span>
                      </div>
                      {comment.content && (
                        <div className="prose prose-sm prose-invert max-w-none text-slate-300">
                          <ReactMarkdown>{comment.content}</ReactMarkdown>
                        </div>
                      )}
                      {comment.attachments && comment.attachments.length > 0 && (
                        <div className="mt-3">
                          <h5 className="text-xs font-semibold text-slate-400 mb-1">Attachments in this comment:</h5>
                          <ul className="list-disc list-inside pl-1 space-y-1">
                            {comment.attachments.map((att: Attachment, attIndex: number) => (
                              <li key={attIndex} className="text-xs text-slate-300">
                                {att.name} ({att.mimeType}, {att.size ? `${(att.size / 1024).toFixed(1)} KB` : 'size N/A'})
                                {/* TODO: Add download link for att.contentId, using att.getSanitizedName() or similar for filename */} 
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-slate-400">No comments for this report.</p>
              )}
            </div>

            {/* Aggregated Attachments Section */}
            {/* Note: The parser's aggregatedAttachments only includes those <= 5MB */}
            {parsedIssue.aggregatedAttachments && Object.keys(parsedIssue.aggregatedAttachments).length > 0 && (
              <div>
                <h3 className="text-2xl font-semibold text-sky-300 mt-8 mb-4 border-b border-slate-700 pb-2">All Relevant Attachments ({Object.keys(parsedIssue.aggregatedAttachments).length})</h3>
                <ul className="list-disc list-inside pl-4 space-y-1">
                  {Object.values(parsedIssue.aggregatedAttachments).map((att: Attachment, index: number) => (
                     <li key={att.contentId || index} className="text-sm text-slate-300">
                        {att.name} ({att.mimeType}, {att.size ? `${(att.size / 1024).toFixed(1)} KB` : 'size N/A'})
                        {/* TODO: Add download link for att.contentId */} 
                      </li>
                  ))}
                </ul>
              </div>
            )}

          </div>
        )}
        {!isLoading && !error && !parsedIssue && (
          <p className="text-slate-400 text-center py-10">No report data found or report is empty.</p>
        )}
      </main>
      <footer className="p-4 text-center text-xs text-slate-500 border-t border-slate-700/50">
        &copy; {new Date().getFullYear()} {process.env.NEXT_PUBLIC_APP_NAME || 'Crbug Snapshot Dashboard'}
      </footer>
    </div>
  );
}
