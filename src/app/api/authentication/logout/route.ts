// src/app/api/authentication/logout/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const COOKIE_NAME = 'auth_token';

export async function POST(request: NextRequest) {
  try {
    cookies().set(COOKIE_NAME, '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: -1, // Expire the cookie
      path: '/',
    });
    // Redirect to login page after logout
    const loginUrl = new URL('/login', request.url);
    // Prevent caching of the logout response if it's a redirect
    const response = NextResponse.redirect(loginUrl, { status: 302 });
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;

  } catch (error) {
    console.error('Logout API error:', error);
    // Return a JSON response for errors
    return NextResponse.json({ message: 'An unexpected error occurred during logout.' }, { status: 500 });
  }
}
