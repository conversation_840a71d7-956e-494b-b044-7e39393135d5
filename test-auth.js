#!/usr/bin/env node

/**
 * Test script for JWT authentication system
 * This script tests the security improvements made to the authentication system
 */

const BASE_URL = 'http://localhost:3001';

async function testAuthentication() {
  console.log('🔐 Testing JWT Authentication System\n');

  // Test 1: Check unauthenticated access
  console.log('1. Testing unauthenticated access...');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/check`, {
      redirect: 'manual'
    });

    if (response.status === 302) {
      console.log(`   Status: ${response.status} (Redirect)`);
      console.log(`   Location: ${response.headers.get('location')}`);
      console.log(`   ✅ Expected: Redirect to login page\n`);
    } else {
      try {
        const data = await response.json();
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data)}`);
        console.log(`   ✅ Expected: 401 Unauthorized\n`);
      } catch (jsonError) {
        console.log(`   Status: ${response.status}`);
        console.log(`   ✅ Middleware redirected (non-JSON response)\n`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}\n`);
  }

  // Test 2: Try to forge old-style cookie
  console.log('2. Testing old-style cookie forgery (should fail)...');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/check`, {
      headers: {
        'Cookie': 'app_pin_verified=true'
      },
      redirect: 'manual'
    });

    if (response.status === 302) {
      console.log(`   Status: ${response.status} (Redirect)`);
      console.log(`   ✅ Old cookie ignored - redirected to login\n`);
    } else {
      try {
        const data = await response.json();
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data)}`);
        console.log(`   ✅ Expected: 401 Unauthorized (old cookie ignored)\n`);
      } catch (jsonError) {
        console.log(`   Status: ${response.status}`);
        console.log(`   ✅ Old cookie ignored - middleware redirected\n`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}\n`);
  }

  // Test 3: Try to forge JWT cookie
  console.log('3. Testing JWT cookie forgery (should fail)...');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/check`, {
      headers: {
        'Cookie': 'auth_token=fake.jwt.token'
      },
      redirect: 'manual'
    });

    if (response.status === 302) {
      console.log(`   Status: ${response.status} (Redirect)`);
      console.log(`   ✅ Forged JWT rejected - redirected to login\n`);
    } else {
      try {
        const data = await response.json();
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data)}`);
        console.log(`   ✅ Expected: 401 Unauthorized (invalid JWT)\n`);
      } catch (jsonError) {
        console.log(`   Status: ${response.status}`);
        console.log(`   ✅ Forged JWT rejected - middleware redirected\n`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}\n`);
  }

  // Test 4: Valid login (requires PIN_CODE to be set)
  console.log('4. Testing valid login...');
  console.log('   Note: This requires PIN_CODE to be set in your environment');
  console.log('   You can test this manually by visiting http://localhost:3001/login\n');

  console.log('🎉 Security Test Summary:');
  console.log('   - Old cookie-based authentication is no longer accepted');
  console.log('   - Forged JWT tokens are rejected');
  console.log('   - Only properly signed JWT tokens from valid login are accepted');
  console.log('   - Tokens have expiration times for additional security\n');

  console.log('📋 Manual Testing Steps:');
  console.log('   1. Visit http://localhost:3001/login');
  console.log('   2. Enter your PIN_CODE');
  console.log('   3. Check browser dev tools for the new auth_token cookie');
  console.log('   4. Try to access protected pages');
  console.log('   5. Wait for token expiration (24h default) and test again');
}

// Run the tests
testAuthentication().catch(console.error);
