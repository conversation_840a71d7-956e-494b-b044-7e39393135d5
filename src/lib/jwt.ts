// src/lib/jwt.ts
import { SignJWT, jwtVerify } from 'jose';

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Convert secret to Uint8Array for jose
const secret = new TextEncoder().encode(JWT_SECRET);

export interface JWTPayload {
  authenticated: boolean;
  iat?: number;
  exp?: number;
}

/**
 * Parse duration string to seconds
 */
function parseDuration(duration: string): number {
  const match = duration.match(/^(\d+)([hdm])$/);
  if (!match) return 24 * 60 * 60; // Default to 24 hours

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 'h': return value * 60 * 60;
    case 'd': return value * 24 * 60 * 60;
    case 'm': return value * 60;
    default: return 24 * 60 * 60;
  }
}

/**
 * Generate a JWT token for authenticated users
 */
export async function generateToken(): Promise<string> {
  const payload: JWTPayload = {
    authenticated: true,
  };

  const expirationTime = `${parseDuration(JWT_EXPIRES_IN)}s`;

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expirationTime)
    .sign(secret);
}

/**
 * Verify and decode a JWT token
 * @param token - The JWT token to verify
 * @returns The decoded payload if valid, null if invalid
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload as JWTPayload;
  } catch (error) {
    // Token is invalid, expired, or malformed
    console.error('JWT verification failed:', error);
    return null;
  }
}

/**
 * Check if a JWT token is valid and not expired
 * @param token - The JWT token to check
 * @returns true if valid and authenticated, false otherwise
 */
export async function isValidToken(token: string): Promise<boolean> {
  const payload = await verifyToken(token);
  return payload !== null && payload.authenticated === true;
}

/**
 * Get the expiration time of a JWT token
 * @param token - The JWT token
 * @returns Date object of expiration time, or null if invalid
 */
export async function getTokenExpiration(token: string): Promise<Date | null> {
  const payload = await verifyToken(token);
  if (payload && payload.exp) {
    return new Date(payload.exp * 1000);
  }
  return null;
}
